# 惠而信标签打印小程序功能设计与业务流程文档

## 1. 概述
本文档旨在详细说明"惠而信标签打印"微信小程序的功能设计和业务流程。
惠而信标签打印小程序是一款用于标签设计与打印的工具。用户可通过手机蓝牙连接专用的标签打印机，选择内置的标签模板，填写内容后快速完成标签的打印。小程序主要面向需要进行物品标识、食品管理、效期提醒等场景的用户，并为有特殊需求的用户提供标签定制服务。

## 2. 功能详述
### 2.1 打印机连接管理
此模块负责小程序与硬件打印机的连接与状态管理。
#### 2.1.1 连接状态提示
- 在小程序主界面顶部，设有固定的状态提示栏，用于实时显示打印机的连接状态。
- 未连接状态：显示黄色警告图标及文字"未连接打印机"。
- 连接中状态：显示连接图标及文字"正在连接打印机..."。
- 已连接状态：显示绿色对勾图标及文字"已连接打印机【设备SN】"。
- 打印中状态：显示打印图标及文字"正在打印中..."。
- 错误状态：显示红色错误图标及具体错误信息和错误代码。

#### 2.1.2 发起连接
- 在未连接状态下，用户可点击状态提示栏右侧的"去连接"按钮，发起蓝牙连接流程。
- 用户也可以通过底部的"开始打印"按钮，系统会自动引导用户先连接打印机。

#### 2.1.3 连接指引
- 点击"去连接"按钮后，系统会弹出"连接设备"对话框，提供清晰的连接步骤指引，内容如下：
  - 请确保打印机已打开并显示黄灯。
  - 请确保手机的蓝牙功能已开启。
  - 请保持手机与打印机在2米范围内。
  - 点击【去连接设备】，即可连接打印机。
- 对话框提供"暂不连接"和"去连接设备"两个操作按钮，供用户选择。

#### 2.1.4 设备选择
- 用户点击"去连接设备"后，系统会自动搜索附近的蓝牙打印设备。
- 搜索结果以列表形式展示，显示设备名称或设备ID。
- 已连接的设备会标记"已连接"状态。
- 正在连接的设备会显示"连接中..."状态。
- 用户点击列表中的设备即可发起连接。

#### 2.1.5 连接状态管理
- 连接成功后，状态栏会更新为已连接状态，显示设备SN号。
- 连接失败时，会显示具体的错误信息和错误代码。
- 已连接状态下，用户可点击"断开连接"按钮主动断开与打印机的连接。
- 打印过程中，用户可点击"停止"按钮中止当前打印任务。

### 2.2 标签模板选择
小程序提供丰富的预设模板，方便用户根据不同需求快速选择和使用。
#### 2.2.1 模板分类
- 模板库按用途分为四大类：通用标签、食品标签、效期标签、定制标签。
- 用户可通过点击界面顶部的分类Tab进行快速切换。
- 分类信息从服务器动态获取，确保分类始终保持最新。

#### 2.2.2 模板浏览与选择
- 在各分类下，以卡片形式横向滚动展示具体的标签模板（如："存储标签2"、"食品留样标签1"等）。
- 用户可左右滑动浏览当前分类下的所有模板。
- 点击任一模板卡片即可完成选择，下方预览区和内容编辑区将同步更新。
- 模板列表支持分页加载，当用户滑动到列表末尾时，系统会自动加载更多模板。

#### 2.2.3 新用户引导
- 为帮助新用户快速上手，首次进入此界面时，会在模板区域显示气泡提示："左右滑动查看更多模板，点击模板完成选择"。
- 用户可点击模板区域右上角的"?"图标，随时查看模板使用帮助。

#### 2.2.4 模板记忆功能
- 系统会记住用户上次使用的模板，下次进入小程序时自动选中该模板。
- 当用户切换分类时，如果之前在该分类下选择过模板，系统会自动定位到该模板。

#### 2.2.5 滚动指示器
- 当模板列表可以左右滚动时，界面会显示左右滚动指示器，提示用户还有更多模板可以查看。
- 滚动到边界时，相应方向的指示器会自动隐藏。

### 2.3 标签内容编辑与预览
用户选择模板后，可对标签的具体内容进行个性化编辑。
#### 2.3.1 标签预览
- 界面中部为"标签预览"区域，实时、直观地展示当前所选模板的样式及已填写的内容。
- 预览图上方会注明模板的分类和名称（如："默认分类 / 存储标签2"）。
- 预览图会随用户输入的内容实时更新，提供所见即所得的编辑体验。
- 用户可长按预览图保存到手机相册。

#### 2.3.2 内容填写
- 预览图下方为"标签内容"编辑区，提供与模板字段一一对应的输入框（如：品名、最高存储量、最低存储量、责任人）。
- 每个输入框均有字符数限制提示（如：0/6）。
- 输入框内有占位提示文字"（留空则只打印标签模板）"，明确告知用户即使不填写内容，也可直接打印出空白格式的标签。

#### 2.3.3 多种输入类型支持
- 文本输入：普通文本字段，支持字符数限制。
- 日期选择：提供日期选择器，并有"今日"快捷按钮和"清空"按钮。
- 日期时间选择：同时提供日期和时间选择器，有"今天"、"现在"快捷按钮和"清空"按钮。
- 复选框：提供多选项的复选框组，用户可勾选多个选项。
- 下拉选择：提供下拉选择器，用户可从预设选项中选择一项，也可选择"其他"并输入自定义内容。

#### 2.3.4 打印份数设置
- 在内容编辑区底部，提供打印份数设置，用户可通过"+"、"-"按钮或直接输入数字来调整打印份数。
- 默认打印份数为1份。

#### 2.3.5 执行打印
- 完成内容编辑后，用户可点击界面底部的"开始打印"按钮，将编辑好的标签数据通过蓝牙发送至已连接的打印机进行打印。
- 打印过程中，按钮会变为"停止打印"，用户可随时中止打印任务。
- 打印完成后，系统会显示"打印成功"的提示。
- 如果打印过程中出现错误，系统会显示具体的错误信息。
- 打印操作有15秒的超时保护，超时后会自动取消打印并提示用户。

### 2.4 联系我们
为用户提供官方联系渠道，方便获取支持或进行业务咨询。
#### 2.4.1 功能入口
- 在界面底部，打印按钮旁设有"联系我们"的电话图标入口。

#### 2.4.2 功能描述
- 点击该图标后，系统会弹出"联系我们"信息卡片，包含以下详细联系方式：
  - 服务热线：400-622-9388
  - 服务传真：020-89577250
  - 服务邮箱：<EMAIL>
  - 公司地址：广州市海珠区泉塘路 2 号之三（浩诚商务中心）605 惠而信
  - 官方小程序入口：点击后会提示"即将打开'惠而信'小程序"，用于跳转到公司的另一个小程序。

#### 2.4.3 便捷复制功能
- 用户可点击联系方式（如电话、邮箱等）直接复制内容，方便后续使用。

### 2.5 定制标签服务推广
为满足用户的个性化需求，小程序内设有定制标签服务的推广入口。
#### 2.5.1 功能描述
- 当标准模板无法满足用户特定业务需求时，可通过此功能联系官方进行专属标签的设计与定制。

#### 2.5.2 展现形式
- 该功能以弹窗广告的形式在小程序启动或特定操作后触发，向用户展示专业的标签定制能力和案例。
- 在模板列表的最后一项也设有"定制标签"入口，点击后会显示定制服务信息。

#### 2.5.3 内容与交互
- 推广内容：弹窗内展示精心设计的推广海报，主题围绕"专属标签定制"，突出企业的专业服务能力。
- 联系方式：海报中会提供清晰的联系方式，如客服二维码和热线电话。
- 引导操作：通过"长按图片识别二维码添加客服"等文字引导，方便用户快速与业务人员建立联系，进行定制咨询。
- 关闭选项：弹窗右上角提供关闭按钮（"X"），用户可随时关闭，该功能不会影响小程序核心打印功能的使用。

### 2.6 用户体验优化功能
#### 2.6.1 自定义导航栏
- 小程序采用自定义导航栏设计，顶部显示品牌logo和"标签打印"标题。
- 导航栏高度会根据不同设备平台（iOS/Android）自动调整，确保在各种设备上都有良好的显示效果。

#### 2.6.2 数据持久化
- 系统会自动保存用户上次填写的标签内容，下次进入小程序时自动恢复，提高使用效率。
- 用户选择的模板和分类也会被记住，便于连续打印相同类型的标签。

#### 2.6.3 错误处理与提示
- 系统对各种可能的错误情况（如蓝牙连接失败、打印超时等）都有明确的提示和处理机制。
- 错误提示包含具体的错误代码和描述，便于用户排查问题或寻求技术支持。

#### 2.6.4 权限管理
- 首次使用蓝牙功能时，系统会自动请求必要的权限（如位置权限、蓝牙权限）。
- 如果用户拒绝授权，系统会提供明确的引导，说明权限的必要性并指导用户如何手动开启权限。

## 3. 技术实现要点
### 3.1 蓝牙通信
- 小程序使用微信蓝牙API与打印机建立连接，支持设备搜索、连接、数据传输等功能。
- 采用专用的打印机通信协议（SUPVANAPIT50PRO），确保与硬件设备的稳定通信。

### 3.2 模板渲染
- 使用Canvas技术在前端实时渲染标签预览图，支持文本、条形码、二维码等多种元素。
- 模板数据采用结构化格式存储，支持服务端动态更新和管理。

### 3.3 数据存储
- 使用微信小程序的本地存储功能（wx.setStorageSync/wx.getStorageSync）保存用户偏好和临时数据。
- 关键业务数据（如打印记录）会上传至服务端进行持久化存储和分析。

### 3.4 用户标识
- 使用微信OpenID作为用户唯一标识，用于关联用户的打印记录和使用习惯。
- 系统会在后台定期检查OpenID的可用性，确保数据关联的准确性。

## 4. 业务流程
### 4.1 标准打印流程
1. 用户进入小程序，系统自动加载模板分类和模板列表。
2. 用户选择需要的模板分类和具体模板。
3. 系统根据所选模板生成预览图和对应的输入表单。
4. 用户填写标签内容，设置打印份数。
5. 用户点击"开始打印"按钮，系统检查打印机连接状态。
6. 如未连接打印机，系统引导用户完成打印机连接。
7. 连接成功后，系统将标签数据发送至打印机进行打印。
8. 打印完成后，系统显示打印结果并记录打印信息。

### 4.2 定制标签流程
1. 用户通过模板列表中的"定制标签"入口或弹窗广告了解定制服务。
2. 用户长按广告图片，扫描客服二维码添加客服。
3. 用户与客服沟通定制需求，提供必要的设计素材和要求。
4. 客服团队完成标签设计后，将定制模板添加到用户的"定制标签"分类中。
5. 用户可在"定制标签"分类下找到并使用专属定制的模板。

## 5. 未来功能规划
### 5.1 模板编辑器
- 计划开发在线模板编辑器，允许用户自行设计简单的标签模板。
- 支持拖拽式编辑，可添加文本、图片、条形码等元素。

### 5.2 批量打印功能
- 支持导入Excel/CSV数据，批量生成多个标签并连续打印。
- 适用于需要大量打印不同内容标签的场景。

### 5.3 打印历史记录
- 记录用户的打印历史，支持快速重复打印历史标签。
- 提供打印统计分析，帮助用户了解标签使用情况。

### 5.4 云端模板同步
- 实现跨设备模板同步，用户可在多台设备上使用相同的自定义模板。
- 支持模板分享和导入功能，方便团队协作。

## 6. 附录
### 6.1 错误代码对照表
- 0: 操作成功
- 100: 开始打印
- 116: 模板对象为空
- 132: 打印操作超时
- 其他错误码及其含义...

### 6.2 常见问题解答
1. 为什么无法搜索到打印机？
   - 请确保打印机已开机并处于待连接状态（黄灯常亮）。
   - 请确保手机蓝牙已开启且授予了位置权限。
   - 请保持手机与打印机的距离在2米以内。

2. 打印机连接后突然断开怎么办？
   - 请检查打印机电量是否充足。
   - 尝试重启打印机后重新连接。
   - 如果问题持续，请联系客服获取技术支持。

3. 如何获取定制标签服务？
   - 点击模板列表中的"定制标签"或联系客服。
   - 提供您的具体需求和设计要求。
   - 客服团队会在3个工作日内完成设计并添加到您的账户中。